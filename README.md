# JWT Token Generator API with SFMC Vertex Claude API

A serverless API that generates JWT tokens, provides access to Vertex AI Claude models, and restricts access based on the origin domain.

## Features

- Generates JWT tokens signed with a Google Cloud service account private key
- Provides access to Vertex AI Claude models via AnthropicVertex client
- Supports multiple Claude models (Haiku, Sonnet)
- Implements CORS to allow only specific origin domains
- Runs as a containerized application on Cloud Run

## Prerequisites

- Google Cloud SDK installed
- Docker installed (for local testing)
- Access to the Google Cloud project: `nca-datapl-nonprod-econgen`

## Deployment Instructions

### 1. Install and Configure Google Cloud SDK

If you haven't already installed the Google Cloud SDK, follow these steps:

```bash
# Download and install the Google Cloud SDK
curl https://sdk.cloud.google.com | bash

# Restart your shell
exec -l $SHELL

# Initialize the SDK
gcloud init

# Authenticate with your Google account
gcloud auth login

# Set the project
gcloud config set project nca-datapl-nonprod-econgen

# Set the region (choose the appropriate region)
gcloud config set run/region us-east5
```

### 2. Enable Required Services

```bash
# Enable Cloud Run and Cloud Build
gcloud services enable run.googleapis.com
gcloud services enable cloudbuild.googleapis.com
```

### 3. Deploy to Cloud Run

Navigate to the directory containing the application files and run:

```bash
# Deploy to Cloud Run
gcloud run deploy jwt-endpoint \
  --source . \
  --platform managed \
  --allow-unauthenticated \
  --region us-east5 \
  --service-account <EMAIL>
```

This command will:

1. Build a container image from the source code
2. Push it to Google Container Registry
3. Deploy it to Cloud Run
4. Configure it to use the specified service account

### 4. Test the Endpoint

After deployment, you'll receive a URL for your service. You can test it using curl:

```bash
# Test JWT token endpoint with allowed origin
curl -H "Origin: https://cloud.e.newsdigitalmedia.com.au" https://your-service-url/token

# Test SFMC Vertex Claude API endpoint
curl -X POST -H "Origin: https://cloud.e.newsdigitalmedia.com.au" -H "Content-Type: application/json" \
  -d '{"model":"claude-3-5-haiku@********","messages":[{"role":"user","content":"Write a haiku about clouds."}]}' \
  https://your-service-url/sfmc-vertex-claude-api

# Test with disallowed origin (should return 403)
curl -H "Origin: https://example.com" https://your-service-url/token
```

## Local Testing

To test the application locally before deployment:

```bash
# Build the Docker image
docker build -t jwt-endpoint .

# Run the container
docker run -p 8080:8080 jwt-endpoint

# Test the local JWT token endpoint
curl -H "Origin: https://cloud.e.newsdigitalmedia.com.au" http://localhost:8080/token

# Test the local SFMC Vertex Claude API endpoint
curl -X POST -H "Origin: https://cloud.e.newsdigitalmedia.com.au" -H "Content-Type: application/json" \
  -d '{"model":"claude-3-5-haiku@********","messages":[{"role":"user","content":"Write a haiku about clouds."}]}' \
  http://localhost:8080/sfmc-vertex-claude-api
```

## Security Considerations

- The service account key is included in the container. In a production environment, consider using Secret Manager to store sensitive credentials.
- The API allows unauthenticated access but restricts based on the Origin header. This is not foolproof as the Origin header can be spoofed.
- For additional security, consider implementing IP-based restrictions or token-based authentication.

## Customization

- To add more allowed origins, modify the `ALLOWED_ORIGINS` list in `main.py`.
- To change the token expiration time, modify the `exp` field in the payload.
- To add custom claims to the token, send a POST request with a JSON body containing a `claims` object.
- To add support for additional Claude models, update the `supported_models` list in the `sfmc_vertex_claude_api` function.

## SFMC Integration

The API includes an example SSJS script (`sfmc_vertex_claude_example.ssjs`) that demonstrates how to call the SFMC Vertex Claude API endpoint from Salesforce Marketing Cloud. This script:

- Makes a POST request to the endpoint with the appropriate headers and payload
- Handles the response and extracts the content
- Is compatible with ECMAScript 3 or lower for SFMC
- Uses eval() instead of parseJSON for parsing JSON responses

To use this script in SFMC:

1. Copy the script to your SFMC account
2. Update the `ENDPOINT_URL` and `ALLOWED_ORIGIN` variables as needed
3. Customize the prompt and parameters as needed
4. Execute the script in a CloudPage or other SFMC context
