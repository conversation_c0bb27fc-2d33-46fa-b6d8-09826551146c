#!/usr/bin/env python3
"""
Test script for JWT token generation and Vertex AI API call
"""

import os
import json
import requests
import google.auth
import google.auth.transport.requests
from google.oauth2 import service_account

# Configuration
SERVICE_ACCOUNT_PATH = 'service-account.json'
SCOPES = [
    'https://www.googleapis.com/auth/cloud-platform',
    'https://www.googleapis.com/auth/cloud-platform.read-only'
]

def generate_gcp_access_token():
    """Generate a Google Cloud OAuth 2.0 access token"""
    try:
        # Load the service account credentials
        credentials = service_account.Credentials.from_service_account_file(
            SERVICE_ACCOUNT_PATH, scopes=SCOPES)

        # Get the access token
        credentials.refresh(google.auth.transport.requests.Request())

        return {
            'token': credentials.token,
            'expires_in': 3600,  # Typically 1 hour
            'token_type': 'Bearer'
        }
    except Exception as e:
        print(f"Error generating GCP access token: {e}")
        return None

def test_vertex_ai(token_data):
    """Test the token with Vertex AI API"""
    try:
        # Test the token with a different API
        # Let's try the direct Anthropic API instead of Vertex AI
        url = "https://api.anthropic.com/v1/messages"

        # Let's try a different approach - let's use the Google Cloud Storage API
        # which should definitely work with our token
        project_id = "nca-datapl-nonprod-econgen"
        url = f"https://storage.googleapis.com/storage/v1/b?project={project_id}"

        headers = {
            "Authorization": f"Bearer {token_data['token']}",
            "Content-Type": "application/json"
        }

        # No data needed for a GET request to GCS
        data = None

        print("Making request to Google Cloud Storage API...")
        print(f"URL: {url}")
        print(f"Headers: {headers}")

        response = requests.get(url, headers=headers)

        print(f"Response status code: {response.status_code}")
        print(f"Response body: {response.text}")

        if response.status_code == 200:
            return {
                'success': True,
                'vertex_response': response.json()
            }
        else:
            return {
                'success': False,
                'error': f"Vertex API error: {response.status_code}",
                'details': response.text
            }

    except Exception as e:
        print(f"Error testing Vertex AI: {e}")
        return {'error': 'Failed to test Vertex AI', 'details': str(e)}

def main():
    """Main function"""
    print("Generating GCP access token...")
    token_data = generate_gcp_access_token()

    if not token_data:
        print("Failed to generate GCP access token")
        return

    print(f"Token generated successfully: {token_data['token'][:20]}...")

    print("\nTesting Vertex AI API...")
    result = test_vertex_ai(token_data)

    print("\nTest result:")
    print(json.dumps(result, indent=2))

if __name__ == "__main__":
    main()
