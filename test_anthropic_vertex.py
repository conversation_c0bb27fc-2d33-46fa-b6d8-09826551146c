#!/usr/bin/env python3
"""
Test script for making a call to Vertex AI using the Anthropic Vertex client
"""

import json
import requests
import os
from google.oauth2.credentials import Credentials

# Configuration
JWT_ENDPOINT_URL = "https://jwt-endpoint-bzgagdx2vq-ul.a.run.app/gcp-token"
ALLOWED_ORIGIN = "https://cloud.e.newsdigitalmedia.com.au"
PROJECT_ID = "nca-datapl-nonprod-econgen"
LOCATION = "us-central1"  # Using a supported Vertex AI region
MODEL_ID = "claude-3-5-haiku@20241022"  # Using the model ID from the example

def get_gcp_token():
    """Get a GCP token from our endpoint"""
    headers = {
        "Origin": ALLOWED_ORIGIN
    }

    print(f"Getting token from {JWT_ENDPOINT_URL}...")
    response = requests.get(JWT_ENDPOINT_URL, headers=headers)

    if response.status_code == 200:
        token_data = response.json()
        print(f"Token received successfully: {token_data['token'][:20]}...")
        return token_data
    else:
        print(f"Error getting token: {response.status_code}")
        print(response.text)
        return None

def call_vertex_ai_with_anthropic_client(token_data):
    """Call Vertex AI with the Anthropic Vertex client"""
    try:
        # First, let's install the anthropic-vertex package if it's not already installed
        try:
            import anthropic
            from anthropic import AnthropicVertex
            print("Anthropic Vertex client already installed")
        except ImportError:
            print("Installing Anthropic Vertex client...")
            import subprocess
            subprocess.check_call(["pip", "install", "anthropic-vertex"])
            from anthropic import AnthropicVertex
            print("Anthropic Vertex client installed")

        # Create credentials from the token
        credentials = Credentials(token=token_data['token'])

        # Create the Anthropic Vertex client
        print(f"Creating Anthropic Vertex client for region {LOCATION} and project {PROJECT_ID}...")
        client = AnthropicVertex(
            region=LOCATION,
            project_id=PROJECT_ID,
            credentials=credentials
        )

        # Make the request
        print(f"Making request to Claude model {MODEL_ID}...")
        message = client.messages.create(
            max_tokens=1024,
            messages=[
                {
                    "role": "user",
                    "content": "Write a haiku about clouds."
                }
            ],
            model=MODEL_ID
        )

        print("Response received!")
        print(json.dumps(message.model_dump(), indent=2))

        return {
            'success': True,
            'response': message.model_dump()
        }
    except Exception as e:
        print(f"Error calling Vertex AI with Anthropic client: {e}")
        return {
            'success': False,
            'error': str(e)
        }

def main():
    """Main function"""
    token_data = get_gcp_token()

    if not token_data:
        print("Failed to get GCP token")
        return

    print("\nCalling Vertex AI with Anthropic client...")
    result = call_vertex_ai_with_anthropic_client(token_data)

    print("\nFinal result:")
    print(json.dumps(result, indent=2))

if __name__ == "__main__":
    main()
