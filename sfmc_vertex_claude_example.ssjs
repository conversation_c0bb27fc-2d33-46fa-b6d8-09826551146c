<script runat=server>
Platform.Load("Core","1");

/**
 * Example SSJS code for calling the SFMC Vertex Claude API endpoint
 * This code is compatible with ECMAScript 3 or lower for Salesforce Marketing Cloud
 */

// Configuration
var ENDPOINT_URL = "https://jwt-endpoint-bzgagdx2vq-ul.a.run.app/sfmc-vertex-claude-api";
var ALLOWED_ORIGIN = "https://cloud.e.newsdigitalmedia.com.au";

// Debug logging function - using simple timestamp for ECMAScript 3 compatibility
var debugLog = [];
function addLog(log, message) {
    var now = new Date();
    var timestamp = now.getFullYear() + "-" +
                   (now.getMonth() + 1) + "-" +
                   now.getDate() + " " +
                   now.getHours() + ":" +
                   now.getMinutes() + ":" +
                   now.getSeconds();
    log.push(timestamp + ": " + message);
    return log;
}

// Generate a unique request ID
var requestId = "req_" + Platform.Function.GUID().replace(/-/g, "").substring(0, 16);
debugLog = addLog(debugLog, "Request ID: " + requestId);

Write("<h2>DEBUG: Starting execution</h2>");
Write("<p>ENDPOINT_URL: " + ENDPOINT_URL + "</p>");
Write("<p>ALLOWED_ORIGIN: " + ALLOWED_ORIGIN + "</p>");

/**
 * Function to call the SFMC Vertex Claude API endpoint
 * @param {string} userPrompt - The user prompt to send to Claude
 * @param {string} systemPrompt - Optional system prompt
 * @param {string} model - Optional model ID (defaults to claude-3-5-haiku@20241022)
 * @returns {object} - The response from Claude
 */
function callVertexClaude(userPrompt, systemPrompt, model) {
    debugLog = addLog(debugLog, "Entering callVertexClaude function");
    debugLog = addLog(debugLog, "User prompt: " + userPrompt);
    debugLog = addLog(debugLog, "System prompt: " + (systemPrompt || "not provided"));
    debugLog = addLog(debugLog, "Model: " + (model || "not provided"));

    var response = {
        success: false,
        error: null,
        content: null,
        requestId: requestId,
        debugLog: debugLog
    };

    try {
        // Set default values if not provided
        systemPrompt = systemPrompt || "";
        model = model || "claude-3-5-haiku@20241022";

        debugLog = addLog(debugLog, "Using model: " + model);

        // Create the request payload
        var payload = {
            "model": model,
            "messages": [
                {
                    "role": "user",
                    "content": userPrompt
                }
            ],
            "max_tokens": 1024,
            "temperature": 0.7,
            "top_p": 0.95,
            "top_k": 40,
            "system": systemPrompt
        };

        // Convert payload to JSON string
        var payloadString = Platform.Function.Stringify(payload);
        debugLog = addLog(debugLog, "Request payload created");

        // Add URL parameters to help bypass caching
        var timestamp = new Date().getTime();
        var randomParam = Math.floor(Math.random() * 1000000);
        var endpoint = ENDPOINT_URL + "?t=" + timestamp + "&r=" + randomParam;

        debugLog = addLog(debugLog, "Using endpoint: " + endpoint);

        // Create and configure the HTTP request
        debugLog = addLog(debugLog, "Creating Script.Util.HttpRequest object");
        var req = new Script.Util.HttpRequest(endpoint);
        req.emptyContentHandling = 0;
        req.retries = 2;
        req.continueOnError = true;
        req.method = "POST";

        // Set content type directly as shown in the working example
        req.contentType = "application/json;charset=UTF-8";
        debugLog = addLog(debugLog, "Set contentType: " + req.contentType);

        // Add headers
        req.setHeader("Accept", "application/json");
        req.setHeader("Cache-Control", "no-cache");

        // Set Origin header to the allowed origin
        req.setHeader("Origin", ALLOWED_ORIGIN);
        debugLog = addLog(debugLog, "Set Origin header: " + ALLOWED_ORIGIN);

        // Set timeout (in seconds, not milliseconds)
        req.timeout = 60; // 60 seconds timeout
        debugLog = addLog(debugLog, "Set request timeout to 60 seconds");

        req.postData = payloadString;

        // Send the request and measure response time
        var apiStartTime = new Date().getTime();
        debugLog = addLog(debugLog, "Sending request to API with Script.Util.HttpRequest");

        // Log all request details before sending
        debugLog = addLog(debugLog, "Request method: " + req.method);
        debugLog = addLog(debugLog, "Request URL: " + endpoint);
        debugLog = addLog(debugLog, "Request payload: " + payloadString);

        // Send the request
        var resp = req.send();
        var apiEndTime = new Date().getTime();
        debugLog = addLog(debugLog, "API call took " + (apiEndTime - apiStartTime) + "ms");

        // Check if we received a response
        if (!resp) {
            debugLog = addLog(debugLog, "No response received from API");
            response.error = "No response received from API";
            return response;
        }

        // Parse status code safely
        var statusCode = 0;
        try {
            statusCode = parseInt(resp.statusCode);
            if (isNaN(statusCode)) statusCode = 0;
        } catch(e) {
            statusCode = 0;
        }
        debugLog = addLog(debugLog, "API response status: " + statusCode);

        // Log error content if status is not 200
        if (statusCode !== 200 && resp.content) {
            debugLog = addLog(debugLog, "Raw error content: " + String(resp.content));
            response.error = "API returned status code " + statusCode;
            response.statusCode = statusCode;
            return response;
        }

        // Process successful response
        if (statusCode === 200 && resp.content) {
            var respContent = String(resp.content);
            debugLog = addLog(debugLog, "Response length: " + respContent.length);

            // Try to parse the JSON response
            try {
                // Use eval() for ECMAScript 3 compatibility
                var responseObj;
                try {
                    // Safely wrap the eval in parentheses
                    responseObj = eval("(" + respContent + ")");
                    debugLog = addLog(debugLog, "Response parsed with eval()");
                } catch (evalErr) {
                    debugLog = addLog(debugLog, "eval() parsing failed: " + evalErr.message);
                    // Try Platform.Function.ParseJSON as fallback
                    try {
                        responseObj = Platform.Function.ParseJSON(respContent);
                        debugLog = addLog(debugLog, "Response parsed with Platform.Function.ParseJSON");
                    } catch (parseJsonErr) {
                        throw new Error("Failed to parse response: " + parseJsonErr.message);
                    }
                }

                response.success = true;
                response.content = responseObj;
                return response;
            } catch (parseErr) {
                debugLog = addLog(debugLog, "Failed to parse API response: " + parseErr.message);
                response.error = "Failed to parse API response";
                return response;
            }
        } else {
            debugLog = addLog(debugLog, "Request failed with status: " + statusCode);
            response.error = "Request failed with status: " + statusCode;
            response.statusCode = statusCode;
            return response;
        }
    } catch (e) {
        debugLog = addLog(debugLog, "Exception in callVertexClaude: " + e.message);
        response.error = "Exception: " + e.message;
        return response;
    }
}

/**
 * Function to extract text content from Claude's response
 * @param {object} claudeResponse - The response from Claude
 * @returns {string} - The extracted text content
 */
function extractClaudeContent(claudeResponse) {
    debugLog = addLog(debugLog, "Entering extractClaudeContent function");

    try {
        if (!claudeResponse) {
            debugLog = addLog(debugLog, "claudeResponse is null or undefined");
            return "Error: Invalid response (null or undefined)";
        }

        if (!claudeResponse.success) {
            debugLog = addLog(debugLog, "claudeResponse.success is false");
            return "Error: " + (claudeResponse.error || "Request was not successful");
        }

        if (!claudeResponse.content) {
            debugLog = addLog(debugLog, "claudeResponse.content is missing");
            return "Error: Response content is missing";
        }

        var response = claudeResponse.content;

        // Extract content from the response
        if (response.content && response.content.length > 0) {
            debugLog = addLog(debugLog, "Found content array with " + response.content.length + " items");

            var contentItems = [];

            // Loop through content items
            for (var i = 0; i < response.content.length; i++) {
                var item = response.content[i];

                if (item.text) {
                    contentItems.push(item.text);
                }
            }

            var result = contentItems.join("\n");
            debugLog = addLog(debugLog, "Extracted " + contentItems.length + " text items");
            return result;
        } else {
            debugLog = addLog(debugLog, "No content array found or it's empty");

            // Try alternative response formats
            if (response.text) {
                debugLog = addLog(debugLog, "Found text property in response");
                return response.text;
            }

            if (typeof response == 'string') {
                debugLog = addLog(debugLog, "Response is a string");
                return response;
            }
        }

        return "No content found in response";
    } catch (e) {
        debugLog = addLog(debugLog, "Exception in extractClaudeContent: " + e.message);
        return "Error extracting content: " + e.message;
    }
}

// Main execution
try {
    debugLog = addLog(debugLog, "Starting execution");

    // Call Claude with the prompt
    var userPrompt = "Write a short email about a summer sale.";
    var systemPrompt = "You are a helpful marketing assistant that writes concise, engaging content.";
    var modelName = "claude-3-5-haiku@20241022";

    debugLog = addLog(debugLog, "Calling Claude with prompt: " + userPrompt);
    var result = callVertexClaude(userPrompt, systemPrompt, modelName);

    // Output the result
    if (result.success) {
        debugLog = addLog(debugLog, "API call successful");

        // Extract and display just the text content
        var textContent = extractClaudeContent(result);

        // Display the results
        Write("<h2>Success!</h2>");
        Write("<h3>Claude Response:</h3>");
        Write("<pre>" + Stringify(result.content) + "</pre>");

        Write("<h3>Extracted Content:</h3>");
        Write("<pre>" + textContent + "</pre>");
    } else {
        debugLog = addLog(debugLog, "API call failed: " + result.error);

        Write("<h2>Error</h2>");
        Write("<p>Status Code: " + (result.statusCode || "Not provided") + "</p>");
        Write("<p>Error: " + (result.error || "No error message") + "</p>");
    }

    // Always output debug log
    Write("<h3>Debug Log:</h3>");
    Write("<pre>");
    for (var i = 0; i < debugLog.length; i++) {
        Write(debugLog[i] + "\n");
    }
    Write("</pre>");

} catch (e) {
    Write("<h2>Exception:</h2>");
    Write("<p>" + e.message + "</p>");

    // Output debug log even on exception
    Write("<h3>Debug Log:</h3>");
    Write("<pre>");
    for (var i = 0; i < debugLog.length; i++) {
        Write(debugLog[i] + "\n");
    }
    Write("</pre>");
}
</script>
