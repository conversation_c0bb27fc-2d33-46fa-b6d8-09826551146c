#!/usr/bin/env python3
"""
Test script for making a call to Vertex AI using the JWT token from our endpoint
"""

import json
import requests

# Configuration
JWT_ENDPOINT_URL = "https://jwt-endpoint-bzgagdx2vq-ul.a.run.app/gcp-token"
ALLOWED_ORIGIN = "https://cloud.e.newsdigitalmedia.com.au"
PROJECT_ID = "nca-datapl-nonprod-econgen"
LOCATION = "us-east5"
MODEL_ID = "claude-3-haiku@20240307"

def get_gcp_token():
    """Get a GCP token from our endpoint"""
    headers = {
        "Origin": ALLOWED_ORIGIN
    }
    
    print(f"Getting token from {JWT_ENDPOINT_URL}...")
    response = requests.get(JWT_ENDPOINT_URL, headers=headers)
    
    if response.status_code == 200:
        token_data = response.json()
        print(f"Token received successfully: {token_data['token'][:20]}...")
        return token_data
    else:
        print(f"Error getting token: {response.status_code}")
        print(response.text)
        return None

def call_vertex_ai(token_data):
    """Call Vertex AI with the token"""
    # Vertex AI API URL for Claude model
    url = f"https://{LOCATION}-aiplatform.googleapis.com/v1/projects/{PROJECT_ID}/locations/{LOCATION}/publishers/anthropic/models/{MODEL_ID}:predict"
    
    headers = {
        "Authorization": f"Bearer {token_data['token']}",
        "Content-Type": "application/json"
    }
    
    # Try different request formats
    formats = []
    
    # Format 1: Standard Vertex AI format
    formats.append({
        "name": "Standard Vertex AI format",
        "data": {
            "instances": [
                {
                    "prompt": "Human: Write a haiku about clouds.\n\nAssistant:"
                }
            ],
            "parameters": {
                "temperature": 0.2,
                "maxOutputTokens": 100,
                "topK": 40,
                "topP": 0.95
            }
        }
    })
    
    # Format 2: Another Vertex AI format
    formats.append({
        "name": "Another Vertex AI format",
        "data": {
            "instances": [
                {
                    "messages": [
                        {"role": "user", "content": "Write a haiku about clouds."}
                    ]
                }
            ],
            "parameters": {
                "temperature": 0.2,
                "maxOutputTokens": 100,
                "topK": 40,
                "topP": 0.95
            }
        }
    })
    
    # Format 3: Direct Claude API format
    formats.append({
        "name": "Direct Claude API format",
        "data": {
            "system": "",
            "messages": [
                {"role": "user", "content": "Write a haiku about clouds."}
            ],
            "temperature": 0.2,
            "max_tokens": 100,
            "top_p": 0.95,
            "top_k": 40,
            "anthropic_version": "vertex-2023-10-16"
        }
    })
    
    # Format 4: Simplified format
    formats.append({
        "name": "Simplified format",
        "data": {
            "prompt": "Write a haiku about clouds."
        }
    })
    
    # Format 5: Vertex AI format with content
    formats.append({
        "name": "Vertex AI format with content",
        "data": {
            "contents": [
                {
                    "role": "user",
                    "parts": [
                        {"text": "Write a haiku about clouds."}
                    ]
                }
            ],
            "generation_config": {
                "temperature": 0.2,
                "max_output_tokens": 100,
                "top_p": 0.95,
                "top_k": 40
            }
        }
    })
    
    # Try all formats
    for format_info in formats:
        format_name = format_info["name"]
        data = format_info["data"]
        
        print(f"\nTrying {format_name}...")
        print(f"URL: {url}")
        print(f"Headers: {headers}")
        print(f"Data: {json.dumps(data, indent=2)}")
        
        response = requests.post(url, headers=headers, json=data)
        
        print(f"Response status code: {response.status_code}")
        print(f"Response body: {response.text}")
        
        if response.status_code == 200:
            print("Success!")
            return {
                'success': True,
                'format_used': format_name,
                'vertex_response': response.json()
            }
    
    # If all formats failed, try a different approach
    print("\nTrying to use Google Cloud Storage API to verify token...")
    storage_url = f"https://storage.googleapis.com/storage/v1/b?project={PROJECT_ID}"
    
    storage_headers = {
        "Authorization": f"Bearer {token_data['token']}",
        "Content-Type": "application/json"
    }
    
    storage_response = requests.get(storage_url, headers=storage_headers)
    
    print(f"Storage API response status code: {storage_response.status_code}")
    print(f"Storage API response body: {storage_response.text}")
    
    if storage_response.status_code in [200, 403]:  # 403 is expected if the service account doesn't have permission
        print("Token is valid! The issue is with the Vertex AI API request format or permissions.")
        return {
            'success': False,
            'token_valid': True,
            'error': "All Vertex AI formats failed, but token is valid"
        }
    else:
        return {
            'success': False,
            'token_valid': False,
            'error': "All formats failed and token may be invalid"
        }

def main():
    """Main function"""
    token_data = get_gcp_token()
    
    if not token_data:
        print("Failed to get GCP token")
        return
    
    print("\nCalling Vertex AI...")
    result = call_vertex_ai(token_data)
    
    print("\nFinal result:")
    print(json.dumps(result, indent=2))

if __name__ == "__main__":
    main()
