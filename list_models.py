#!/usr/bin/env python3
"""
Script to list available models in Vertex AI
"""

import json
import requests
from google.cloud import aiplatform
from google.oauth2 import service_account
import google.auth.transport.requests

# Configuration
SERVICE_ACCOUNT_PATH = 'service-account.json'
PROJECT_ID = "nca-datapl-nonprod-econgen"
LOCATIONS = ["us-east5", "us-central1"]  # Try multiple regions

def get_gcp_token():
    """Get a GCP token from the service account"""
    credentials = service_account.Credentials.from_service_account_file(
        SERVICE_ACCOUNT_PATH,
        scopes=["https://www.googleapis.com/auth/cloud-platform"]
    )
    
    # Get the access token
    credentials.refresh(google.auth.transport.requests.Request())
    
    return {
        'token': credentials.token,
        'expires_in': 3600,  # Typically 1 hour
        'token_type': 'Bearer'
    }

def list_models_with_sdk():
    """List models using the SDK"""
    try:
        for location in LOCATIONS:
            print(f"\nListing models in {location} using SDK...")
            
            # Initialize the SDK with the service account
            aiplatform.init(
                project=PROJECT_ID,
                location=location,
                credentials=service_account.Credentials.from_service_account_file(SERVICE_ACCOUNT_PATH)
            )
            
            # List available models
            models = aiplatform.Model.list()
            print(f"Found {len(models)} models in {location}:")
            for model in models:
                print(f"- {model.display_name}")
    except Exception as e:
        print(f"Error listing models with SDK: {e}")

def list_models_with_api(token_data):
    """List models using direct API calls"""
    try:
        for location in LOCATIONS:
            print(f"\nListing models in {location} using API...")
            
            # Try to list models
            url = f"https://{location}-aiplatform.googleapis.com/v1/projects/{PROJECT_ID}/locations/{location}/models"
            
            headers = {
                "Authorization": f"Bearer {token_data['token']}",
                "Content-Type": "application/json"
            }
            
            response = requests.get(url, headers=headers)
            
            print(f"Response status code: {response.status_code}")
            if response.status_code == 200:
                models = response.json().get("models", [])
                print(f"Found {len(models)} models:")
                for model in models:
                    print(f"- {model.get('displayName')}")
            else:
                print(f"Error: {response.text}")
            
            # Try to list publisher models
            print(f"\nListing publisher models in {location}...")
            publisher_url = f"https://{location}-aiplatform.googleapis.com/v1/projects/{PROJECT_ID}/locations/{location}/publishers/google/models"
            
            publisher_response = requests.get(publisher_url, headers=headers)
            
            print(f"Response status code: {publisher_response.status_code}")
            if publisher_response.status_code == 200:
                publisher_models = publisher_response.json().get("models", [])
                print(f"Found {len(publisher_models)} publisher models:")
                for model in publisher_models:
                    print(f"- {model.get('name')}")
            else:
                print(f"Error: {publisher_response.text}")
    except Exception as e:
        print(f"Error listing models with API: {e}")

def main():
    """Main function"""
    print("Getting GCP token...")
    token_data = get_gcp_token()
    
    print(f"Token received: {token_data['token'][:20]}...")
    
    print("\nListing models with SDK...")
    list_models_with_sdk()
    
    print("\nListing models with API...")
    list_models_with_api(token_data)

if __name__ == "__main__":
    main()
