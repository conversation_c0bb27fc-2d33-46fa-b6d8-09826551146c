#!/usr/bin/env python3
"""
Test script for making a call to Vertex AI using the token from our endpoint
"""

import json
import requests

# Configuration
ENDPOINT_URL = "https://jwt-endpoint-bzgagdx2vq-ul.a.run.app/gcp-token"
ALLOWED_ORIGIN = "https://cloud.e.newsdigitalmedia.com.au"

def get_gcp_token():
    """Get a GCP token from our endpoint"""
    headers = {
        "Origin": ALLOWED_ORIGIN
    }
    
    response = requests.get(ENDPOINT_URL, headers=headers)
    
    if response.status_code == 200:
        return response.json()
    else:
        print(f"Error getting token: {response.status_code}")
        print(response.text)
        return None

def call_vertex_ai(token_data):
    """Call Vertex AI with the token"""
    # Vertex AI configuration
    project_id = "nca-datapl-nonprod-econgen"
    location = "us-east5"
    model_id = "claude-3-haiku@20240307"
    
    url = f"https://{location}-aiplatform.googleapis.com/v1/projects/{project_id}/locations/{location}/publishers/anthropic/models/{model_id}:predict"
    
    headers = {
        "Authorization": f"Bearer {token_data['token']}",
        "Content-Type": "application/json"
    }
    
    # Try different request formats
    
    # Format 1: Direct Claude API format
    data1 = {
        "system": "",
        "messages": [
            {"role": "user", "content": "Write a haiku about clouds."}
        ],
        "temperature": 0.2,
        "max_tokens": 100,
        "top_p": 0.95,
        "top_k": 40,
        "anthropic_version": "vertex-2023-10-16"
    }
    
    # Format 2: Vertex AI format
    data2 = {
        "instances": [
            {
                "prompt": "Human: Write a haiku about clouds.\n\nAssistant:"
            }
        ],
        "parameters": {
            "temperature": 0.2,
            "maxOutputTokens": 100,
            "topK": 40,
            "topP": 0.95
        }
    }
    
    # Format 3: Another Vertex AI format
    data3 = {
        "instances": [
            {
                "messages": [
                    {"role": "user", "content": "Write a haiku about clouds."}
                ]
            }
        ],
        "parameters": {
            "temperature": 0.2,
            "maxOutputTokens": 100,
            "topK": 40,
            "topP": 0.95
        }
    }
    
    # Try all formats
    formats = [
        ("Direct Claude API format", data1),
        ("Vertex AI format", data2),
        ("Another Vertex AI format", data3)
    ]
    
    for format_name, data in formats:
        print(f"\nTrying {format_name}...")
        print(f"URL: {url}")
        print(f"Headers: {headers}")
        print(f"Data: {json.dumps(data, indent=2)}")
        
        response = requests.post(url, headers=headers, json=data)
        
        print(f"Response status code: {response.status_code}")
        print(f"Response body: {response.text}")
        
        if response.status_code == 200:
            print("Success!")
            return {
                'success': True,
                'format_used': format_name,
                'vertex_response': response.json()
            }
    
    return {
        'success': False,
        'error': "All formats failed"
    }

def main():
    """Main function"""
    print("Getting GCP token from endpoint...")
    token_data = get_gcp_token()
    
    if not token_data:
        print("Failed to get GCP token")
        return
    
    print(f"Token received successfully: {token_data['token'][:20]}...")
    
    print("\nCalling Vertex AI...")
    result = call_vertex_ai(token_data)
    
    print("\nFinal result:")
    print(json.dumps(result, indent=2))

if __name__ == "__main__":
    main()
