#!/usr/bin/env python3
"""
Test script for making a call to Vertex AI using the Google Cloud SDK
"""

import json
import requests
from google.cloud import aiplatform
from google.oauth2 import service_account
import google.auth.transport.requests

# Configuration
SERVICE_ACCOUNT_PATH = 'service-account.json'
PROJECT_ID = "nca-datapl-nonprod-econgen"
LOCATION = "us-east5"
MODEL_ID = "claude-3-haiku@********"

def get_gcp_token():
    """Get a GCP token from the service account"""
    credentials = service_account.Credentials.from_service_account_file(
        SERVICE_ACCOUNT_PATH,
        scopes=["https://www.googleapis.com/auth/cloud-platform"]
    )

    # Get the access token
    credentials.refresh(google.auth.transport.requests.Request())

    return {
        'token': credentials.token,
        'expires_in': 3600,  # Typically 1 hour
        'token_type': 'Bearer'
    }

def call_vertex_ai_direct(token_data):
    """Call Vertex AI directly with the token"""
    url = f"https://{LOCATION}-aiplatform.googleapis.com/v1/projects/{PROJECT_ID}/locations/{LOCATION}/publishers/anthropic/models/{MODEL_ID}:predict"

    headers = {
        "Authorization": f"Bearer {token_data['token']}",
        "Content-Type": "application/json"
    }

    # Try different request formats

    # Format 1: Direct Claude API format
    data1 = {
        "system": "",
        "messages": [
            {"role": "user", "content": "Write a haiku about clouds."}
        ],
        "temperature": 0.2,
        "max_tokens": 100,
        "top_p": 0.95,
        "top_k": 40,
        "anthropic_version": "vertex-2023-10-16"
    }

    # Format 2: Vertex AI format
    data2 = {
        "instances": [
            {
                "prompt": "Human: Write a haiku about clouds.\n\nAssistant:"
            }
        ],
        "parameters": {
            "temperature": 0.2,
            "maxOutputTokens": 100,
            "topK": 40,
            "topP": 0.95
        }
    }

    # Format 3: Another Vertex AI format
    data3 = {
        "instances": [
            {
                "messages": [
                    {"role": "user", "content": "Write a haiku about clouds."}
                ]
            }
        ],
        "parameters": {
            "temperature": 0.2,
            "maxOutputTokens": 100,
            "topK": 40,
            "topP": 0.95
        }
    }

    # Try all formats
    formats = [
        ("Direct Claude API format", data1),
        ("Vertex AI format", data2),
        ("Another Vertex AI format", data3)
    ]

    for format_name, data in formats:
        print(f"\nTrying {format_name}...")
        print(f"URL: {url}")
        print(f"Headers: {headers}")
        print(f"Data: {json.dumps(data, indent=2)}")

        response = requests.post(url, headers=headers, json=data)

        print(f"Response status code: {response.status_code}")
        print(f"Response body: {response.text}")

        if response.status_code == 200:
            print("Success!")
            return {
                'success': True,
                'format_used': format_name,
                'vertex_response': response.json()
            }

    return {
        'success': False,
        'error': "All formats failed"
    }

def call_vertex_ai_sdk():
    """Call Vertex AI using the SDK"""
    try:
        # Initialize the SDK with the service account
        aiplatform.init(
            project=PROJECT_ID,
            location=LOCATION,
            credentials=service_account.Credentials.from_service_account_file(SERVICE_ACCOUNT_PATH)
        )

        # Try using the Vertex AI SDK to make a prediction with a text model
        print("\nTrying to use a text model for prediction...")
        try:
            # Use a text model that's available in the region
            endpoint = aiplatform.Endpoint(
                endpoint_name=f"projects/{PROJECT_ID}/locations/{LOCATION}/publishers/google/models/text-bison"
            )

            response = endpoint.predict(
                instances=[{"prompt": "Write a haiku about clouds."}],
                parameters={
                    "temperature": 0.2,
                    "maxOutputTokens": 100,
                    "topK": 40,
                    "topP": 0.95
                }
            )

            print(f"Prediction response: {response}")
            return {
                'success': True,
                'message': "SDK prediction completed",
                'response': response
            }
        except Exception as e:
            print(f"Error making prediction: {e}")

        # Try using the Vertex AI SDK to make a prediction with Claude
        print("\nTrying to use Claude model for prediction...")
        try:
            # Use the Claude model
            endpoint = aiplatform.Endpoint(
                endpoint_name=f"projects/{PROJECT_ID}/locations/{LOCATION}/publishers/anthropic/models/{MODEL_ID}"
            )

            response = endpoint.predict(
                instances=[{"prompt": "Human: Write a haiku about clouds.\n\nAssistant:"}],
                parameters={
                    "temperature": 0.2,
                    "maxOutputTokens": 100,
                    "topK": 40,
                    "topP": 0.95
                }
            )

            print(f"Prediction response: {response}")
            return {
                'success': True,
                'message': "SDK prediction completed",
                'response': response
            }
        except Exception as e:
            print(f"Error making prediction with Claude: {e}")

        return {
            'success': False,
            'message': "SDK operations completed but no successful predictions"
        }
    except Exception as e:
        print(f"Error using SDK: {e}")
        return {
            'success': False,
            'error': str(e)
        }

def main():
    """Main function"""
    print("Getting GCP token...")
    token_data = get_gcp_token()

    print(f"Token received: {token_data['token'][:20]}...")

    print("\nCalling Vertex AI directly...")
    direct_result = call_vertex_ai_direct(token_data)

    print("\nCalling Vertex AI using SDK...")
    sdk_result = call_vertex_ai_sdk()

    print("\nFinal results:")
    print("Direct API call result:")
    print(json.dumps(direct_result, indent=2))
    print("\nSDK call result:")
    print(json.dumps(sdk_result, indent=2))

if __name__ == "__main__":
    main()
