#!/bin/bash
# Deployment script for JWT Token Generator API with SFMC Vertex Claude API

# Exit on error
set -e

# Configuration
PROJECT_ID="nca-datapl-nonprod-econgen"
REGION="us-east5"
SERVICE_NAME="jwt-endpoint"
SERVICE_ACCOUNT="<EMAIL>"

# Print banner
echo "====================================================="
echo "  Deploying JWT Token Generator API with SFMC Vertex Claude API to Cloud Run"
echo "====================================================="
echo "Project: $PROJECT_ID"
echo "Region: $REGION"
echo "Service: $SERVICE_NAME"
echo "Service Account: $SERVICE_ACCOUNT"
echo "====================================================="

# Set gcloud path
GCLOUD_PATH="../bin/gcloud"
if [ ! -f "$GCLOUD_PATH" ]; then
    echo "Error: Google Cloud SDK (gcloud) is not found at $GCLOUD_PATH."
    echo "Please check the path to gcloud."
    exit 1
fi

# Check if user is authenticated
if ! $GCLOUD_PATH auth list --filter=status:ACTIVE --format="value(account)" &> /dev/null; then
    echo "You are not authenticated with gcloud. Please run '$GCLOUD_PATH auth login'."
    exit 1
fi

# Set project
echo "Setting project to $PROJECT_ID..."
$GCLOUD_PATH config set project $PROJECT_ID

# Set region
echo "Setting region to $REGION..."
$GCLOUD_PATH config set run/region $REGION

# Enable required services
echo "Enabling required services..."
$GCLOUD_PATH services enable run.googleapis.com
$GCLOUD_PATH services enable cloudbuild.googleapis.com

# Enable Artifact Registry API
echo "Enabling Artifact Registry API..."
$GCLOUD_PATH services enable artifactregistry.googleapis.com

# Create Artifact Registry repository if it doesn't exist
REPO_NAME="cloud-run-deploy"
echo "Creating Artifact Registry repository $REPO_NAME in $REGION..."
$GCLOUD_PATH artifacts repositories create $REPO_NAME \
  --repository-format=docker \
  --location=$REGION \
  --description="Repository for Cloud Run deployments" \
  --quiet || true

# Build and push the Docker image
IMAGE_NAME="$REGION-docker.pkg.dev/$PROJECT_ID/$REPO_NAME/$SERVICE_NAME:latest"
echo "Building and pushing Docker image to $IMAGE_NAME..."
$GCLOUD_PATH builds submit --tag $IMAGE_NAME

# Deploy to Cloud Run
echo "Deploying to Cloud Run..."
$GCLOUD_PATH run deploy $SERVICE_NAME \
  --image $IMAGE_NAME \
  --platform managed \
  --allow-unauthenticated \
  --region $REGION

# Get the service URL
SERVICE_URL=$($GCLOUD_PATH run services describe $SERVICE_NAME --format="value(status.url)")

echo "====================================================="
echo "Deployment completed successfully!"
echo "Service URL: $SERVICE_URL"
echo "====================================================="
echo "Test with allowed origin:"
echo "curl -H \"Origin: https://cloud.e.newsdigitalmedia.com.au\" $SERVICE_URL/token"
echo ""
echo "Test SFMC Vertex Claude API endpoint:"
echo "curl -X POST -H \"Origin: https://cloud.e.newsdigitalmedia.com.au\" -H \"Content-Type: application/json\" -d '{\"model\":\"claude-3-5-haiku@20241022\",\"messages\":[{\"role\":\"user\",\"content\":\"Write a haiku about clouds.\"}]}' $SERVICE_URL/sfmc-vertex-claude-api"
echo "====================================================="
