#!/usr/bin/env python3
"""
Flask application that generates JWT tokens and implements CORS for domain whitelisting
"""

import os
import json
import datetime
from datetime import timezone
import time
import uuid
from flask import Flask, request, jsonify
import jwt
from functools import wraps
import google.auth
import google.auth.transport.requests
from google.oauth2 import service_account
import requests
try:
    from anthropic import AnthropicVertex
except ImportError:
    import subprocess
    subprocess.check_call(["pip", "install", "anthropic[vertex]"])
    from anthropic import AnthropicVertex

app = Flask(__name__)

# Configuration
ALLOWED_ORIGINS = ['https://cloud.e.newsdigitalmedia.com.au']
SECRET_KEY = os.environ.get('JWT_SECRET_KEY', 'default-secret-key')  # Set via environment variable in production
# Use absolute path for service account file
current_dir = os.path.dirname(os.path.abspath(__file__))
SERVICE_ACCOUNT_PATH = os.environ.get('SERVICE_ACCOUNT_PATH', os.path.join(current_dir, 'service-account.json'))

# Vertex AI API scopes
SCOPES = [
    'https://www.googleapis.com/auth/cloud-platform',
    'https://www.googleapis.com/auth/cloud-platform.read-only'
]

# Load service account credentials
def load_service_account():
    try:
        with open(SERVICE_ACCOUNT_PATH, 'r') as f:
            return json.load(f)
    except Exception as e:
        app.logger.error(f"Error loading service account: {e}")
        return None

# Helper function to check if origin is allowed
def is_allowed_origin(origin):
    if not origin:
        return False

    # Check if the origin matches any allowed origin
    for allowed_origin in ALLOWED_ORIGINS:
        if origin == allowed_origin or origin.startswith(allowed_origin + '/'):
            return True

    return False

# CORS decorator to check origin
def cors_check(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        origin = request.headers.get('Origin')

        # Log the origin for debugging
        app.logger.info(f"Request from origin: {origin}")

        # Check if origin is allowed
        if not is_allowed_origin(origin):
            app.logger.warning(f"Blocked request from unauthorized origin: {origin}")
            return jsonify({
                'error': 'Origin not allowed',
                'message': 'This endpoint can only be accessed from authorized domains'
            }), 403

        # Call the original function
        response = f(*args, **kwargs)

        # Add CORS headers
        if isinstance(response, tuple):
            response_obj, status_code = response
            if isinstance(response_obj, dict):
                response_obj = jsonify(response_obj)
            response_obj.headers.add('Access-Control-Allow-Origin', origin)
            response_obj.headers.add('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
            response_obj.headers.add('Access-Control-Allow-Headers', 'Content-Type, Authorization')
            return response_obj, status_code
        else:
            response.headers.add('Access-Control-Allow-Origin', origin)
            response.headers.add('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
            response.headers.add('Access-Control-Allow-Headers', 'Content-Type, Authorization')
            return response

    return decorated_function

# Handle OPTIONS requests for CORS preflight
@app.route('/', methods=['OPTIONS'])
@app.route('/token', methods=['OPTIONS'])
@app.route('/gcp-token', methods=['OPTIONS'])
@app.route('/test-vertex', methods=['OPTIONS'])
@app.route('/sfmc-vertex-claude-api', methods=['OPTIONS'])
def handle_options():
    origin = request.headers.get('Origin')
    if not is_allowed_origin(origin):
        return jsonify({'error': 'Origin not allowed'}), 403

    response = jsonify({'status': 'ok'})
    response.headers.add('Access-Control-Allow-Origin', origin)
    response.headers.add('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
    response.headers.add('Access-Control-Allow-Headers', 'Content-Type, Authorization')
    return response

# Function to generate Google Cloud OAuth 2.0 access token
def generate_gcp_access_token():
    try:
        # Load the service account credentials
        credentials = service_account.Credentials.from_service_account_file(
            SERVICE_ACCOUNT_PATH, scopes=SCOPES)

        # Get the access token
        credentials.refresh(google.auth.transport.requests.Request())

        return {
            'token': credentials.token,
            'expires_in': 3600,  # Typically 1 hour
            'token_type': 'Bearer'
        }
    except Exception as e:
        app.logger.error(f"Error generating GCP access token: {e}")
        return None

# Root endpoint
@app.route('/', methods=['GET'])
@cors_check
def root():
    return jsonify({
        'message': 'JWT token generator API',
        'endpoints': {
            '/token': 'Generate a JWT token',
            '/gcp-token': 'Generate a Google Cloud OAuth 2.0 access token for Vertex AI',
            '/sfmc-vertex-claude-api': 'SFMC endpoint for Vertex AI Claude API integration'
        }
    })

# Token generation endpoint
@app.route('/token', methods=['GET', 'POST'])
@cors_check
def generate_token():
    # Load service account
    service_account = load_service_account()
    if not service_account:
        return jsonify({'error': 'Service account configuration error'}), 500

    # Create token payload
    now = datetime.datetime.now(timezone.utc)
    payload = {
        'iss': service_account['client_email'],  # Issuer
        'sub': service_account['client_email'],  # Subject
        'aud': 'https://cloud.e.newsdigitalmedia.com.au',  # Audience
        'iat': now,  # Issued at time
        'exp': now + datetime.timedelta(hours=1),  # Expiration time (1 hour)
        'project_id': service_account['project_id'],
        'token_type': 'access'
    }

    # Add custom claims if provided in request
    if request.method == 'POST' and request.is_json:
        custom_claims = request.json.get('claims', {})
        # Only allow safe claims to be added
        safe_claims = ['user_id', 'name', 'role', 'permissions']
        for key, value in custom_claims.items():
            if key in safe_claims:
                payload[key] = value

    try:
        # Sign the JWT with the private key
        token = jwt.encode(
            payload,
            service_account['private_key'],
            algorithm='RS256',
            headers={
                'kid': service_account['private_key_id']
            }
        )

        return jsonify({
            'token': token,
            'expires_in': 3600,  # 1 hour in seconds
            'token_type': 'Bearer'
        })

    except Exception as e:
        app.logger.error(f"Error generating token: {e}")
        return jsonify({'error': 'Failed to generate token', 'details': str(e)}), 500

# GCP token generation endpoint
@app.route('/gcp-token', methods=['GET', 'POST'])
@cors_check
def generate_gcp_token():
    # Generate the GCP access token
    token_data = generate_gcp_access_token()

    if not token_data:
        return jsonify({'error': 'Failed to generate GCP access token'}), 500

    return jsonify(token_data)

# Test Vertex AI endpoint
@app.route('/test-vertex', methods=['GET'])
@cors_check
def test_vertex():
    try:
        # Generate the GCP access token
        token_data = generate_gcp_access_token()

        if not token_data:
            return jsonify({'error': 'Failed to generate GCP access token'}), 500

        # Let's test the token with Vertex AI API
        project_id = "nca-datapl-nonprod-econgen"
        location = "us-east5"
        model_id = "claude-3-5-haiku@20241022"

        try:
            # Use the Vertex AI API directly
            url = f"https://{location}-aiplatform.googleapis.com/v1/projects/{project_id}/locations/{location}/publishers/anthropic/models/{model_id}:predict"

            headers = {
                "Authorization": f"Bearer {token_data['token']}",
                "Content-Type": "application/json"
            }

            # Format for Vertex AI API using AnthropicVertex client
            try:
                # Create an AnthropicVertex client
                client = AnthropicVertex(
                    region=location,
                    project_id=project_id
                )

                # Create a message
                response = client.messages.create(
                    max_tokens=100,
                    messages=[
                        {
                            "role": "user",
                            "content": "Write a haiku about clouds."
                        }
                    ],
                    model=model_id,
                    temperature=0.2,
                    top_p=0.95
                )

                # Format the response
                return jsonify({
                    'success': True,
                    'vertex_response': response.content[0].text,
                    'token_used': token_data
                })
            except Exception as e:
                return jsonify({
                    'success': False,
                    'error': f"Error: {type(e).__name__}",
                    'details': str(e),
                    'token_used': token_data
                }), 500

            # Make the request to Vertex AI API
            response = requests.post(url, headers=headers, json=data)

            if response.status_code == 200:
                vertex_response = response.json()

                # Extract the response text
                response_text = ""
                if "predictions" in vertex_response:
                    response_text = vertex_response["predictions"][0]["candidates"][0]["content"]["parts"][0]["text"]

                return jsonify({
                    'success': True,
                    'vertex_response': response_text,
                    'token_used': token_data
                })
            else:
                return jsonify({
                    'success': False,
                    'error': f"Vertex AI API error: {response.status_code}",
                    'details': response.text,
                    'token_used': token_data
                }), 500
        except Exception as e:
            return jsonify({
                'error': f"Error: {type(e).__name__}",
                'details': str(e)
            }), 500


    except Exception as e:
        app.logger.error(f"Error testing Vertex AI: {e}")
        return jsonify({'error': 'Failed to test Vertex AI', 'details': str(e)}), 500

# SFMC Vertex Claude API endpoint
@app.route('/sfmc-vertex-claude-api', methods=['POST'])
@cors_check
def sfmc_vertex_claude_api():
    try:
        # Check if request is JSON
        if not request.is_json:
            return jsonify({
                'error': 'Invalid request format',
                'message': 'Request must be in JSON format'
            }), 400

        # Get request data
        request_data = request.json

        # Validate request data
        if 'messages' not in request_data:
            return jsonify({
                'error': 'Invalid request format',
                'message': 'Request must contain a "messages" field'
            }), 400

        # Get model from request or use default
        model_id = request_data.get('model', 'claude-3-5-haiku@20241022')

        # List of supported models
        supported_models = [
            'claude-3-5-sonnet@20240620',
            'claude-3-7-sonnet@20250219',
            'claude-3-5-sonnet-v2@20241022',
            'claude-3-5-haiku@20241022',
            'claude-3-haiku@20240307'
        ]

        # Validate model
        if model_id not in supported_models:
            return jsonify({
                'error': 'Unsupported model',
                'message': f'Model {model_id} is not supported. Supported models: {", ".join(supported_models)}'
            }), 400

        # Generate the GCP access token
        token_data = generate_gcp_access_token()

        if not token_data:
            return jsonify({'error': 'Failed to generate GCP access token'}), 500

        # Project and location configuration
        project_id = "nca-datapl-nonprod-econgen"
        location = "us-east5"  # Using a supported Vertex AI region

        # Extract parameters from request
        max_tokens = request_data.get('max_tokens', 1024)
        temperature = request_data.get('temperature', 0.7)
        top_p = request_data.get('top_p', 0.95)

        try:
            # Create an AnthropicVertex client
            client = AnthropicVertex(
                region=location,
                project_id=project_id
            )

            # Create a message
            response = client.messages.create(
                max_tokens=max_tokens,
                messages=request_data['messages'],
                model=model_id,
                temperature=temperature,
                top_p=top_p
            )

            # Format the response to match Anthropic's format
            anthropic_response = {
                'id': f"msg_{uuid.uuid4()}",
                'type': 'message',
                'role': 'assistant',
                'content': [
                    {
                        'type': 'text',
                        'text': response.content[0].text
                    }
                ],
                'model': model_id
            }

            return jsonify(anthropic_response)
        except Exception as e:
            return jsonify({
                'error': f"Error: {type(e).__name__}",
                'details': str(e)
            }), 500

    except Exception as e:
        app.logger.error(f"Error in SFMC Vertex Claude API: {e}")
        return jsonify({
            'error': 'Failed to process request',
            'message': str(e)
        }), 500

# Health check endpoint (no CORS check)
@app.route('/health', methods=['GET'])
def health_check():
    return jsonify({'status': 'healthy'})

if __name__ == '__main__':
    # Get port from environment variable or default to 8080
    port = int(os.environ.get('PORT', 8080))

    # Run the Flask app
    app.run(host='0.0.0.0', port=port, debug=False)
