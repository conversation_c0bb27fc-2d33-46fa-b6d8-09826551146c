#!/usr/bin/env python3
"""
Test script for making a direct API call to Google Gemini models using just the token
"""

import json
import requests

# Configuration
JWT_ENDPOINT_URL = "https://jwt-endpoint-bzgagdx2vq-ul.a.run.app/gcp-token"
ALLOWED_ORIGIN = "https://cloud.e.newsdigitalmedia.com.au"
PROJECT_ID = "nca-datapl-nonprod-econgen"
LOCATION = "us-east5"  # Using us-east5 as specified
MODEL_ID = "gemini-pro@001"  # Using Gemini Pro with version tag

def get_gcp_token():
    """Get a GCP token from our endpoint"""
    headers = {
        "Origin": ALLOWED_ORIGIN
    }

    print(f"Getting token from {JWT_ENDPOINT_URL}...")
    response = requests.get(JWT_ENDPOINT_URL, headers=headers)

    if response.status_code == 200:
        token_data = response.json()
        print(f"Token received successfully: {token_data['token'][:20]}...")
        return token_data
    else:
        print(f"Error getting token: {response.status_code}")
        print(response.text)
        return None

def call_gemini_direct_api(token_data):
    """Call Gemini model with direct API call"""
    try:
        # Gemini API URL
        url = f"https://{LOCATION}-aiplatform.googleapis.com/v1/projects/{PROJECT_ID}/locations/{LOCATION}/publishers/google/models/{MODEL_ID}:generateContent"

        headers = {
            "Authorization": f"Bearer {token_data['token']}",
            "Content-Type": "application/json"
        }

        # Standard Gemini API request format
        data = {
            "contents": [
                {
                    "role": "user",
                    "parts": [
                        {"text": "Write a haiku about clouds."}
                    ]
                }
            ],
            "generationConfig": {
                "temperature": 0.2,
                "maxOutputTokens": 100,
                "topK": 40,
                "topP": 0.95
            }
        }

        print(f"Making direct API call to Gemini model {MODEL_ID}...")
        print(f"URL: {url}")
        print(f"Headers: {headers}")
        print(f"Data: {json.dumps(data, indent=2)}")

        response = requests.post(url, headers=headers, json=data)

        print(f"Response status code: {response.status_code}")
        print(f"Response body: {response.text}")

        if response.status_code == 200:
            return {
                'success': True,
                'response': response.json()
            }
        else:
            return {
                'success': False,
                'error': f"API call failed with status code {response.status_code}",
                'details': response.text
            }
    except Exception as e:
        print(f"Error calling Gemini API: {e}")
        return {
            'success': False,
            'error': str(e)
        }

def main():
    """Main function"""
    token_data = get_gcp_token()

    if not token_data:
        print("Failed to get GCP token")
        return

    print("\nCalling Gemini API directly...")
    result = call_gemini_direct_api(token_data)

    print("\nFinal result:")
    print(json.dumps(result, indent=2))

if __name__ == "__main__":
    main()
