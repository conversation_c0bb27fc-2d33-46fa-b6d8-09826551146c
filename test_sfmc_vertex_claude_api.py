#!/usr/bin/env python3
"""
Test script for making a call to the SFMC Vertex Claude API endpoint
"""

import json
import requests
import os

# Configuration
ENDPOINT_URL = "https://jwt-endpoint-bzgagdx2vq-ul.a.run.app/sfmc-vertex-claude-api"  # Cloud endpoint
# ENDPOINT_URL = "https://jwt-endpoint-bzgagdx2vq-ul.a.run.app/sfmc-vertex-claude-api"  # Production
ALLOWED_ORIGIN = "https://cloud.e.newsdigitalmedia.com.au"

def test_sfmc_vertex_claude_api():
    """Test the SFMC Vertex Claude API endpoint"""
    headers = {
        "Origin": ALLOWED_ORIGIN,
        "Content-Type": "application/json"
    }

    # Test payload
    payload = {
        "model": "claude-3-5-haiku@20241022",
        "messages": [
            {
                "role": "user",
                "content": "Write a haiku about clouds."
            }
        ],
        "max_tokens": 100,
        "temperature": 0.7,
        "top_p": 0.95,
        "top_k": 40,
        "system": "You are a helpful assistant that writes haikus."
    }

    print(f"Sending request to {ENDPOINT_URL}...")
    print(f"Payload: {json.dumps(payload, indent=2)}")

    response = requests.post(ENDPOINT_URL, headers=headers, json=payload)

    print(f"Response status code: {response.status_code}")

    if response.status_code == 200:
        response_data = response.json()
        print("\nResponse data:")
        print(json.dumps(response_data, indent=2))

        # Extract and print the content
        if "content" in response_data:
            print("\nGenerated content:")
            print(response_data["content"])

        return {
            'success': True,
            'response': response_data
        }
    else:
        print(f"Error: {response.text}")
        return {
            'success': False,
            'error': response.text
        }

def main():
    """Main function"""
    print("Testing SFMC Vertex Claude API endpoint...")
    result = test_sfmc_vertex_claude_api()

    print("\nTest result:")
    print(json.dumps(result, indent=2))

if __name__ == "__main__":
    main()
